<?php

namespace Database\Seeders;
use App\Models\Country;
use Illuminate\Support\Facades\Http;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class CountrySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $response = Http::get('https://dev.api.pvgis.com/country', [
            'offset' => 0,
            'order_by' => 'id',
            'order' => 'ASC',
            'base_columns' => json_encode([
                'name',
                'code_alpha_2',
                'code_alpha_3',
                'timezone_offset',
                'normalized_name',
                'import_id',
                'status',
            ]), 
            'no_limit' => true,
        ]);

        if ($response->failed()) {
            $this->command->error('Failed to fetch country data from API.');
            return;
        }

        $json = $response->json();

        $countries = $json['data'] ?? [];

        foreach ($countries as $country) {
            Country::updateOrCreate(
                ['code_alpha_2' => $country['code_alpha_2']],
                [
                    'name' => $country['name'],
                    'code_alpha_3' => $country['code_alpha_3'],
                    'timezone_offset' => $country['timezone_offset'],
                    'normalized_name' => $country['normalized_name'],
                    'import_id' => $country['import_id'],
                    'status' => $country['status'],
                ]
            );
        }

        $this->command->info('Countries imported successfully.');
    }
}
