<?php

namespace App\Providers\Filament;

use App\Constants\AnnouncementPlacement;
use App\Filament\Dashboard\Pages\TwoFactorAuth\TwoFactorAuth;
use App\Http\Middleware\SetLanguage;
use Filament\Http\Middleware\Authenticate;
use Filament\Http\Middleware\DisableBladeIconComponents;
use Filament\Http\Middleware\DispatchServingFilamentEvent;
use Filament\Navigation\MenuItem;
use Filament\Navigation\NavigationItem;
use Filament\Pages;
use Filament\Panel;
use Filament\PanelProvider;
use Filament\Support\Colors\Color;
use Filament\View\PanelsRenderHook;
use Filament\Widgets;
use Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse;
use Illuminate\Cookie\Middleware\EncryptCookies;
use Illuminate\Routing\Middleware\SubstituteBindings;
use Illuminate\Session\Middleware\AuthenticateSession;
use Illuminate\Session\Middleware\StartSession;
use Illuminate\Support\Facades\Blade;
use Illuminate\View\Middleware\ShareErrorsFromSession;
use App\Filament\Shared\MyProfileConfig;

class DashboardPanelProvider extends PanelProvider
{
    public function panel(Panel $panel): Panel
    {
        $dashboardDomain = config('app.dashboard_subdomain', 'not_found');
        return $panel
            ->id('dashboard')
            ->domain($dashboardDomain)
            ->colors([
                'primary' => Color::Teal,
            ])
            ->darkMode(false)
            ->userMenuItems([
                MenuItem::make()
                    ->label(__('Admin Panel'))
                    ->visible(
                        fn () => auth()->user()->isAdmin()
                    )
                    ->url(fn () => route('filament.admin.pages.dashboard'))
                    ->icon('heroicon-s-cog-8-tooth'),
                MenuItem::make()
                    ->label(__('2-Factor Authentication'))
                    ->visible(
                        fn () => config('app.two_factor_auth_enabled')
                    )
                    ->url(fn () => TwoFactorAuth::getUrl())
                    ->icon('heroicon-s-cog-8-tooth'),
            ])
            ->navigationItems([
                NavigationItem::make(lt('core.my_account.title'))
                    ->url(fn () => route('filament.dashboard.pages.my-profile'))
                    ->icon('heroicon-o-user-circle'),
                NavigationItem::make(lt('core.my_settings.title'))
                    ->url(fn () => route('filament.dashboard.pages.my-settings'))
                    ->icon('heroicon-o-cog-6-tooth'),
                NavigationItem::make('Policies')
                    ->url(fn () => route('filament.dashboard.pages.policies'))
                    ->sort(10)
                    ->icon('heroicon-o-document-text'),
                // NavigationItem::make('Logout')
                //     ->icon('heroicon-o-arrow-left-on-rectangle')
                //     ->url('javascript:void(0);')
                //     ->attributes([
                //         'x-data' => '{}',
                //         '@click.prevent' => "window.dispatchEvent(new CustomEvent('show-logout-modal'))",
                //     ])
                //     ->sort(999),
            ])
            ->discoverResources(in: app_path('Filament/Dashboard/Resources'), for: 'App\\Filament\\Dashboard\\Resources')
            ->discoverPages(in: app_path('Filament/Dashboard/Pages'), for: 'App\\Filament\\Dashboard\\Pages')
            ->pages([
                Pages\Dashboard::class,
            ])
            ->viteTheme(['resources/css/filament/dashboard/theme-client.css', 'resources/sass/global.scss'])
            ->discoverWidgets(in: app_path('Filament/Dashboard/Widgets'), for: 'App\\Filament\\Dashboard\\Widgets')
            ->widgets([
                Widgets\AccountWidget::class,
            ])
            ->middleware([
                EncryptCookies::class,
                AddQueuedCookiesToResponse::class,
                StartSession::class,
                AuthenticateSession::class,
                ShareErrorsFromSession::class,
                SubstituteBindings::class,
                DisableBladeIconComponents::class,
                DispatchServingFilamentEvent::class,
                SetLanguage::class,
            ])
            ->renderHook(
                PanelsRenderHook::HEAD_START,
                hook: fn () => view('components.layouts.partials.dashboard.head-dashboard')
            )
            ->renderHook(
                PanelsRenderHook::BODY_START,
                hook: fn () => view('components.layouts.partials.dashboard.custom-header')
            )
            // ->renderHook(PanelsRenderHook::SIDEBAR_NAV_START, function () {
            //     // return view('components.layouts.partials.analytics');
            //     return view('components.layouts.partials.sidebar-user-profile');
            // })
            ->renderHook(PanelsRenderHook::SIDEBAR_NAV_END, function () {
                return view('components.layouts.partials.custom-sidebar-links');
            })
            ->renderHook(
                PanelsRenderHook::HEAD_START,
                fn () => '<style>.fi-topbar, .fi-sidebar-header{display:none}</style>'
            )
            ->renderHook(
                PanelsRenderHook::SIDEBAR_NAV_START,
                fn () => '<style>.fi-topbar, .fi-sidebar-header{display:none}</style>'
            )
            ->renderHook(PanelsRenderHook::BODY_START,
                fn (): string => Blade::render("@livewire('announcement.view', ['placement' => '".AnnouncementPlacement::USER_DASHBOARD->value."'])")
            )
            ->renderHook(
                PanelsRenderHook::BODY_END,
                fn () => $this->initTranslation()
            )
            // ->renderHook(PanelsRenderHook::SIDEBAR_NAV_END, function () {
            //     return Blade::render('<livewire:logout-modal />');
            // })
            ->authMiddleware([
                Authenticate::class,
            ])->plugins([
                MyProfileConfig::breezyCorePlugin(),
            ])
            // ->sidebarCollapsibleOnDesktop()
            ->spa();
    }
    private function initTranslation(): string
    {
        $direction = in_array(app()->getLocale(), config('app.rtl_languages')) ? 'rtl' : 'ltr';

        return view('filament.hooks.translation', [
            'direction' => $direction,
        ]);
    }
}
