<?php

namespace App\Providers\Filament;

use App\Http\Middleware\SetLanguage;
use Filament\Http\Middleware\Authenticate;
use Filament\Http\Middleware\DisableBladeIconComponents;
use Filament\Http\Middleware\DispatchServingFilamentEvent;
use Filament\Navigation\MenuItem;
use Filament\Navigation\NavigationGroup;
use Filament\Panel;
use Filament\PanelProvider;
use Filament\Support\Colors\Color;
use Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse;
use Illuminate\Cookie\Middleware\EncryptCookies;
use Illuminate\Routing\Middleware\SubstituteBindings;
use Illuminate\Session\Middleware\AuthenticateSession;
use Illuminate\Session\Middleware\StartSession;
use Illuminate\View\Middleware\ShareErrorsFromSession;
use App\Filament\Shared\MyProfileConfig;
use Filament\View\PanelsRenderHook;

class AdminPanelProvider extends PanelProvider
{
    public function panel(Panel $panel): Panel
    {
        $adminDomain = config('app.admin_subdomain', 'not_found');
        return $panel
            ->default()
            ->id('admin')
            ->domain($adminDomain)
            ->colors([
                'primary' => Color::Amber,
            ])
            ->userMenuItems([
                MenuItem::make()
                    ->label(__('User Dashboard'))
                    ->visible(
                        fn () => true
                    )
                    ->url(fn () => route('filament.dashboard.pages.dashboard'))
                    ->icon('heroicon-s-face-smile'),
            ])
            ->discoverResources(in: app_path('Filament/Admin/Resources'), for: 'App\\Filament\\Admin\\Resources')
            ->discoverPages(in: app_path('Filament/Admin/Pages'), for: 'App\\Filament\\Admin\\Pages')
            ->viteTheme(['resources/css/filament/admin/theme.css', 'resources/css/filament/dashboard/theme.css', 'resources/sass/global.scss'])
            ->pages([

            ])
            ->discoverWidgets(in: app_path('Filament/Admin/Widgets'), for: 'App\\Filament\\Admin\\Widgets')
            ->widgets([
            ])
            ->middleware([
                EncryptCookies::class,
                AddQueuedCookiesToResponse::class,
                StartSession::class,
                AuthenticateSession::class,
                ShareErrorsFromSession::class,
                SubstituteBindings::class,
                DisableBladeIconComponents::class,
                DispatchServingFilamentEvent::class,
                SetLanguage::class,
            ])
            ->renderHook(
                PanelsRenderHook::BODY_END,
                fn () => $this->updateDirection()
            )
            ->authMiddleware([
                Authenticate::class,
            ])
            ->navigationGroups([
                NavigationGroup::make()
                    ->label(lt('core.cms'))
                    ->icon('heroicon-s-rectangle-stack')
                    ->collapsed(),
                NavigationGroup::make()
                    ->label(__('Revenue'))
                    ->icon('heroicon-s-rocket-launch')
                    ->collapsed(),
                NavigationGroup::make()
                    ->label(lt('core.locales'))
                    ->icon('heroicon-o-globe-alt')
                    ->collapsed(),
                NavigationGroup::make()
                    ->label(__('Product Management'))
                    ->icon('heroicon-s-shopping-cart')
                    ->collapsed(),
                NavigationGroup::make()
                    ->label(__('User Management'))
                    ->icon('heroicon-s-users')
                    ->collapsed(),
                NavigationGroup::make()
                    ->label(__('Settings'))
                    ->icon('heroicon-s-cog')
                    ->collapsed(),
                NavigationGroup::make()
                    ->label(__('Announcements'))
                    ->icon('heroicon-s-megaphone')
                    ->collapsed(),
                NavigationGroup::make()
                    ->label(__('Blog'))
                    ->icon('heroicon-s-newspaper')
                    ->collapsed(),
                NavigationGroup::make()
                    ->label(__('Roadmap'))
                    ->icon('heroicon-s-bug-ant')
                    ->collapsed(),
            ])
            ->plugins([
                MyProfileConfig::breezyCorePlugin(),
            ])
            ->sidebarCollapsibleOnDesktop()
            ->spa();
    }

    private function updateDirection(): string
    {
        $direction = in_array(app()->getLocale(), config('app.rtl_languages')) ? 'rtl' : 'ltr';
        $output = '<script>document.documentElement.setAttribute("dir", "' . $direction . '");</script>';
        return $output;
    }
}
