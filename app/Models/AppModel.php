<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class AppModel extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'app';

    protected $fillable = [
        'name',
        'appKey',
        'secret',
        'url',
    ];

    protected $hidden = [
        'secret',
    ];

    protected $casts = [
        // 'secret' => 'hashed',
    ];

    // public function setSecretAttribute($value)
    // {
    //     $this->attributes['secret'] = bcrypt($value);
    // }
}
