<?php

namespace App\Filament\Admin\Pages;

use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Form;
use Filament\Pages\Dashboard\Concerns\HasFiltersForm;

class Dashboard extends \Filament\Pages\Dashboard
{
    use HasFiltersForm;

    public static function getNavigationLabel(): string
    {
        return t('core.dashboard.title');
    }

    public function getTitle(): string
    {
        return t('core.dashboard.title');
    }

    public function filtersForm(Form $form): Form
    {
        return $form->schema([
            Section::make([
                DatePicker::make('start_date')
                    ->default(now()->subYear()->toDateString())
                    ->afterStateHydrated(function (DatePicker $component, ?string $state) {
                        if (! $state) {
                            $component->state(now()->subYear()->toDateString());
                        }
                    })
                    ->label(lt('core.start_date')),
                DatePicker::make('end_date')
                    ->default(date(now()->toDateString()))
                    ->afterStateHydrated(function (DatePicker $component, ?string $state) {
                        if (! $state) {
                            $component->state(now()->toDateString());
                        }
                    })
                    ->label(lt('core.end_date')),
                Select::make('period')->label(lt('core.period'))->options([
                    'day' => t('core.day'),
                    'week' => t('core.week'),
                    'month' => t('core.month'),
                    'year' => t('core.year'),
                ])->default('month'),

            ])->columns(3),
        ]);
    }
}
