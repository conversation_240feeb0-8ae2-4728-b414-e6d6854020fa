<?php

namespace App\Filament\Admin\Resources;

use App\Constants\ConfigValueTypeEnum;
use App\Forms\Components\CustomMonacoEditor;
use App\Models\Config;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use App\Filament\Admin\Resources\ConfigResource\Pages;

class ConfigResource extends Resource
{
    protected static ?string $model = Config::class;

    protected static ?string $navigationIcon = 'heroicon-o-cog-6-tooth';

    protected static ?string $navigationLabel = 'Config Manager';

    protected static ?string $navigationGroup = 'Settings';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('key')
                    ->label(__('Key'))
                    ->required()
                    ->unique(Config::class, 'key', ignoreRecord: true),

                Forms\Components\Select::make('value_type')
                    ->label(__('Value Type'))
                    ->options(ConfigValueTypeEnum::toArray())
                    ->default(ConfigValueTypeEnum::STRING->value)
                    ->required()
                    ->live()
                    ->afterStateUpdated(function () {
                        // This will trigger when the value_type changes
                    }),

                CustomMonacoEditor::make('value')
                    ->label(__('Value'))
                    ->language(function (callable $get) {
                        $valueType = $get('value_type');
                        return $valueType === ConfigValueTypeEnum::JSON->value ? 'json' : 'html';
                    })
                    ->theme('blackboard')
                    ->fontSize('14px')
                    ->height('300px')
                    ->monacoId('config-value-editor')
                    ->enablePreview(function (callable $get) {
                        $valueType = $get('value_type');
                        return $valueType === ConfigValueTypeEnum::HTML->value;
                    }),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('key')
                    ->label(__('Key'))
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('value')
                    ->label(__('Value'))
                    ->limit(50)
                    ->tooltip(function (Tables\Columns\TextColumn $column): ?string {
                        $state = $column->getState();
                        if (strlen($state) <= 50) {
                            return null;
                        }
                        return $state;
                    })
                    ->searchable(),

                Tables\Columns\TextColumn::make('value_type')
                    ->label(__('Value Type'))
                    ->sortable(),

                Tables\Columns\IconColumn::make('deletable')
                    ->label(__('Deletable'))
                    ->boolean()
                    ->sortable(),

                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('Created'))
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('updated_at')
                    ->label(__('Updated'))
                    ->dateTime()
                    ->sortable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make()
                    ->visible(fn (Config $record): bool => $record->deletable)
                    ->requiresConfirmation(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('key')
            ->paginated([10, 25, 50, 100]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListConfigs::route('/'),
            'create' => Pages\CreateConfig::route('/create'),
            'edit' => Pages\EditConfig::route('/{record}/edit'),
        ];
    }
}
