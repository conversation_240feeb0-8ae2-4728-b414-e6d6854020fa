<?php

namespace App\Filament\Admin\Resources;

use App\Constants\TestimonyContextEnum;
use App\Filament\Admin\Resources\TestimonyResource\Pages;
use App\Models\Testimony;
use App\Services\TranslationApiService;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Support\Arr;

class TestimonyResource extends Resource
{
    protected TranslationApiService $translationService;

    protected static ?string $model = Testimony::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function getNavigationGroup(): ?string
    {
        return t('core.cms');
    }

    public static function getModelLabel(): string
    {
        return t('core.testimony.page_title');
    }

    public static function getLanguageOptions(): array
    {
        $languageOptions = [];

        try {
            $translationService = app(TranslationApiService::class);
            $res = $translationService->getAllLanguages(['limit' => 200]);

            $languages = Arr::get($res, 'data', []);

            $languageOptions = collect($languages)->mapWithKeys(function ($lang) {
                $languageFullNative = htmlspecialchars($lang['languageFullNative'] ?? '');
                $iso2 = strtoupper($lang['languageISO2'] ?? '');
                $flagCode = htmlspecialchars($lang['flagCode'] ?? '');

                if (!$iso2 || !$languageFullNative) {
                    return [];
                }

                $fullName = sprintf('%s (%s)', $languageFullNative, $iso2);
                $labelHtml = $flagCode
                    ? "<span class='flag-icon flag-icon-" . strtolower($flagCode) . " rounded me-2'></span>$fullName"
                    : $fullName;

                return [$iso2 => $labelHtml];
            })->toArray();

        } catch (\Throwable $e) {
            report($e);
        }

        return $languageOptions;
    }

    public static function form(Form $form): Form
    {
        $contextOptions = TestimonyContextEnum::toArray();

        return $form
            ->schema([
                Forms\Components\Grid::make(1)
                    ->schema([
                        Forms\Components\Select::make('language_iso_2')
                            ->label(t('core.testimony.language'))
                            ->searchable()
                            ->allowHtml()
                            ->options(self::getLanguageOptions())
                            ->required(),
                        Forms\Components\Select::make('country_id')
                            ->label(t('core.testimony.country'))
                            ->relationship('country', 'name')
                            ->searchable()
                            ->required(),
                        Forms\Components\TextInput::make('name')
                            ->label(t('core.testimony.name'))
                            ->required(),
                        Forms\Components\Textarea::make('testimony')
                            ->label(t('core.testimony.page_title'))
                            ->required(),
                        Forms\Components\Select::make('context')
                            ->label(t('core.testimony.context'))
                            ->options($contextOptions)
                            ->default('global')
                            ->required(),
                        Forms\Components\Toggle::make('is_default')
                            ->label(t('core.testimony.default')),
                    ])
                ]);
    }


    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('language_iso_2')
                    ->label(t('core.testimony.language'))
                    ->sortable(),
                Tables\Columns\TextColumn::make('country.name')
                    ->label(t('core.testimony.country'))
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('name')
                    ->label(t('core.testimony.name'))
                    ->limit(30),
                Tables\Columns\TextColumn::make('testimony')
                    ->label(t('core.testimony.page_title'))
                    ->limit(25),
                Tables\Columns\TextColumn::make('context')
                    ->label(t('core.testimony.context'))
                    ->formatStateUsing(fn ($state) => t('core.testimony.'.strtolower($state)))
                    ->limit(10),
                Tables\Columns\IconColumn::make('is_default')->boolean()
                    ->label(t('core.testimony.default')),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make()
                    ->label(t('core.action.edit')),
                Tables\Actions\DeleteAction::make()
                    ->label(t('core.action.delete')),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                    ->label(t('core.action.bulk_delete')),
                ]),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageTestimonies::route('/'),
        ];
    }
}
