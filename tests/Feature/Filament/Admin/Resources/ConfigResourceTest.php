<?php

namespace Tests\Feature\Filament\Admin\Resources;

use App\Constants\ConfigValueTypeEnum;
use App\Filament\Admin\Resources\ConfigResource;
use App\Models\Config;
use Filament\Actions\DeleteAction;
use Livewire\Livewire;
use Tests\Feature\FeatureTest;

class ConfigResourceTest extends FeatureTest
{
    public function test_config_resource_can_render_list_page()
    {
        $user = $this->createAdminUser();

        Config::factory()->create([
            'key' => 'test_key',
            'value' => 'test_value',
            'value_type' => ConfigValueTypeEnum::STRING->value,
            'deletable' => true,
        ]);

        $this->actingAs($user);

        $response = $this->get(ConfigResource::getUrl('index'));

        $response->assertSuccessful();
    }

    public function test_config_resource_can_render_create_page()
    {
        $user = $this->createAdminUser();

        $this->actingAs($user);

        $response = $this->get(ConfigResource::getUrl('create'));

        $response->assertSuccessful();
    }

    public function test_config_resource_can_render_edit_page()
    {
        $user = $this->createAdminUser();

        $config = Config::factory()->create([
            'key' => 'test_key',
            'value' => 'test_value',
            'value_type' => ConfigValueTypeEnum::STRING->value,
            'deletable' => true,
        ]);

        $this->actingAs($user);

        $response = $this->get(ConfigResource::getUrl('edit', ['record' => $config]));

        $response->assertSuccessful();
    }

    public function test_config_resource_can_create_config()
    {
        $user = $this->createAdminUser();

        $this->actingAs($user);

        $newData = [
            'key' => 'new_test_key',
            'value' => 'new_test_value',
            'value_type' => ConfigValueTypeEnum::STRING->value,
        ];

        Livewire::test(ConfigResource\Pages\CreateConfig::class)
            ->fillForm($newData)
            ->call('create')
            ->assertHasNoFormErrors();

        $this->assertDatabaseHas('configs', $newData);
    }

    public function test_config_resource_can_edit_config()
    {
        $user = $this->createAdminUser();

        $config = Config::factory()->create([
            'key' => 'test_key',
            'value' => 'test_value',
            'value_type' => ConfigValueTypeEnum::STRING->value,
            'deletable' => true,
        ]);

        $this->actingAs($user);

        $newData = [
            'key' => 'updated_test_key',
            'value' => 'updated_test_value',
            'value_type' => ConfigValueTypeEnum::JSON->value,
        ];

        Livewire::test(ConfigResource\Pages\EditConfig::class, ['record' => $config->getRouteKey()])
            ->fillForm($newData)
            ->call('save')
            ->assertHasNoFormErrors();

        $this->assertDatabaseHas('configs', $newData);
    }

    public function test_config_resource_can_delete_deletable_config()
    {
        $user = $this->createAdminUser();

        $config = Config::factory()->create([
            'key' => 'test_key',
            'value' => 'test_value',
            'value_type' => ConfigValueTypeEnum::STRING->value,
            'deletable' => true,
        ]);

        $this->actingAs($user);

        Livewire::test(ConfigResource\Pages\EditConfig::class, ['record' => $config->getRouteKey()])
            ->callAction(DeleteAction::class);

        $this->assertModelMissing($config);
    }
}
